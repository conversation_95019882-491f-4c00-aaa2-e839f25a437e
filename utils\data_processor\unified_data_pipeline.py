#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一数据处理管道 - 集成所有重构后的组件

该模块提供统一的数据处理入口，集成类型管理、错误处理、验证系统和调试信息。

作者: Augment Agent
创建时间: 2025-07-31
版本: 1.0.0
"""

import pandas as pd
from typing import List, Dict, Any, Optional, Union
import time
from dataclasses import dataclass

from utils.logger import get_unified_logger, LogTarget
from utils.data_processor.data_type_manager import DataTypeManager
from utils.error_handling.unified_error_handler import (
    error_handler, ErrorCode, ErrorCategory, ErrorSeverity
)
from utils.debug.debug_info_system import debug_system
from utils.validation.smart_validation_system import smart_validator
from utils.config.config_manager import config_manager
from data.storage.enhanced_parquet_reader import EnhancedParquetReader

@dataclass
class PipelineResult:
    """管道处理结果"""
    success: bool
    data: Optional[pd.DataFrame]
    processing_time: float
    statistics: Dict[str, Any]
    warnings: List[str]
    errors: List[str]

class UnifiedDataPipeline:
    """统一数据处理管道"""
    
    def __init__(self, pipeline_name: str = "default"):
        """
        初始化统一数据处理管道
        
        Args:
            pipeline_name: 管道名称，用于日志和调试
        """
        self.pipeline_name = pipeline_name
        self.config = config_manager.get_config()
        self.logger = get_unified_logger(__name__, enhanced=True)
        
        # 初始化组件
        self.data_type_manager = DataTypeManager(
            strict_mode=(self.config.data_processing.validation_level.value == "strict"),
            auto_fix=self.config.data_processing.auto_fix_types
        )
        
        self.enhanced_reader = EnhancedParquetReader(
            enable_type_checking=self.config.data_processing.strict_type_checking,
            enable_performance_monitoring=self.config.data_processing.enable_performance_monitoring
        )
        
        # 统计信息
        self.pipeline_statistics = {
            "total_runs": 0,
            "successful_runs": 0,
            "total_processing_time": 0.0,
            "average_processing_time": 0.0,
            "type_fixes": 0,
            "validation_failures": 0
        }
        
    def process_data(self, operation: str, **kwargs) -> PipelineResult:
        """
        统一数据处理入口
        
        Args:
            operation: 操作类型 ('read_partitioned', 'merge_dataframes', 'validate_data')
            **kwargs: 操作参数
            
        Returns:
            PipelineResult: 处理结果
        """
        start_time = time.time()
        self.pipeline_statistics["total_runs"] += 1
        
        # 创建错误上下文
        context = error_handler.create_context(
            function_name="process_data",
            module_name="unified_data_pipeline",
            operation=operation,
            pipeline_name=self.pipeline_name,
            **kwargs
        )
        
        # 启动数据流跟踪
        if self.config.debug.enable_data_flow_tracking:
            debug_system.data_flow_tracker.start_flow(
                f"{self.pipeline_name}_{operation}"
            )
            
        # 启动性能监控
        with debug_system.performance_monitor.monitor_operation(
            f"{self.pipeline_name}_{operation}"
        ):
            try:
                # 根据操作类型分发处理
                if operation == "read_partitioned":
                    result = self._process_read_partitioned(**kwargs)
                elif operation == "merge_dataframes":
                    result = self._process_merge_dataframes(**kwargs)
                elif operation == "validate_data":
                    result = self._process_validate_data(**kwargs)
                elif operation == "type_safe_concat":
                    result = self._process_type_safe_concat(**kwargs)
                else:
                    raise ValueError(f"不支持的操作类型: {operation}")
                    
                # 更新统计信息
                processing_time = time.time() - start_time
                self.pipeline_statistics["total_processing_time"] += processing_time
                
                if result.success:
                    self.pipeline_statistics["successful_runs"] += 1
                    
                self.pipeline_statistics["average_processing_time"] = (
                    self.pipeline_statistics["total_processing_time"] / 
                    self.pipeline_statistics["total_runs"]
                )
                
                # 结束数据流跟踪
                if self.config.debug.enable_data_flow_tracking:
                    flow_summary = debug_system.data_flow_tracker.end_flow()
                    result.statistics["flow_summary"] = flow_summary
                    
                self.logger.info(LogTarget.FILE,
                          f"管道处理完成: {operation}, 成功: {result.success}, "
                          f"耗时: {processing_time:.3f}s")
                
                return result
                
            except Exception as e:
                # 处理异常
                error_info = error_handler.handle_error(
                    ErrorCode.SYSTEM,
                    f"管道处理异常: {str(e)}",
                    context,
                    details=str(e),
                    suggestions=[
                        "检查输入参数是否正确",
                        "查看详细错误日志",
                        "验证数据格式"
                    ]
                )
                
                processing_time = time.time() - start_time
                
                return PipelineResult(
                    success=False,
                    data=None,
                    processing_time=processing_time,
                    statistics={"error": str(e)},
                    warnings=[],
                    errors=[str(e)]
                )
                
    def _process_read_partitioned(self, **kwargs) -> PipelineResult:
        """处理分区数据读取"""
        required_params = ["data_root", "symbol", "period"]
        for param in required_params:
            if param not in kwargs:
                return PipelineResult(
                    success=False,
                    data=None,
                    processing_time=0.0,
                    statistics={},
                    warnings=[],
                    errors=[f"缺少必需参数: {param}"]
                )
                
        # 使用增强读取器读取数据
        data = self.enhanced_reader.read_partitioned_data(**kwargs)
        
        warnings = []
        errors = []
        
        if data is None:
            errors.append("数据读取失败")
            return PipelineResult(
                success=False,
                data=None,
                processing_time=0.0,
                statistics={},
                warnings=warnings,
                errors=errors
            )
            
        # 数据验证
        if self.config.validation.enable_type_validation:
            validation_result = smart_validator.batch_validate_dataframe(data)
            if validation_result.get("issue_count", 0) > 0:
                warnings.extend(validation_result.get("recommendations", []))
                
        return PipelineResult(
            success=True,
            data=data,
            processing_time=0.0,  # 在外层计算
            statistics={
                "data_shape": data.shape,
                "reader_stats": self.enhanced_reader.get_statistics(),
                "validation_result": validation_result if 'validation_result' in locals() else {}
            },
            warnings=warnings,
            errors=errors
        )
        
    def _process_merge_dataframes(self, dataframes: List[pd.DataFrame], 
                                **kwargs) -> PipelineResult:
        """处理DataFrame合并"""
        if not dataframes:
            return PipelineResult(
                success=False,
                data=None,
                processing_time=0.0,
                statistics={},
                warnings=[],
                errors=["没有提供要合并的DataFrame"]
            )
            
        # 使用类型安全合并
        context_name = kwargs.get("context", "merge_operation")
        merged_data = self.data_type_manager.safe_concat_with_type_check(
            dataframes, context_name
        )
        
        warnings = []
        errors = []
        
        if merged_data is None:
            errors.append("DataFrame合并失败")
            return PipelineResult(
                success=False,
                data=None,
                processing_time=0.0,
                statistics={},
                warnings=warnings,
                errors=errors
            )
            
        # 合并后验证
        if self.config.validation.enable_type_validation:
            analysis = self.data_type_manager.analyze_dataframe_types(
                merged_data, f"{context_name}_merged"
            )
            
            if analysis.get("issues"):
                warnings.extend(analysis.get("recommendations", []))
                
        return PipelineResult(
            success=True,
            data=merged_data,
            processing_time=0.0,
            statistics={
                "input_dataframes": len(dataframes),
                "merged_shape": merged_data.shape,
                "type_analysis": analysis if 'analysis' in locals() else {}
            },
            warnings=warnings,
            errors=errors
        )
        
    def _process_validate_data(self, data: pd.DataFrame, 
                             validation_type: str = "full", **kwargs) -> PipelineResult:
        """处理数据验证"""
        if data is None or data.empty:
            return PipelineResult(
                success=False,
                data=data,
                processing_time=0.0,
                statistics={},
                warnings=[],
                errors=["数据为空，无法验证"]
            )
            
        warnings = []
        errors = []
        validation_results = {}
        
        # 类型验证
        if validation_type in ["full", "type"]:
            type_analysis = self.data_type_manager.analyze_dataframe_types(
                data, "validation"
            )
            validation_results["type_analysis"] = type_analysis
            
            if type_analysis.get("issues"):
                warnings.extend(type_analysis.get("recommendations", []))
                
        # 时间戳验证
        if validation_type in ["full", "timestamp"]:
            time_column = kwargs.get("time_column", "time")
            if time_column in data.columns:
                timestamp_validation = smart_validator.batch_validate_dataframe(
                    data, time_column
                )
                validation_results["timestamp_validation"] = timestamp_validation
                
                if timestamp_validation.get("issue_count", 0) > 0:
                    warnings.extend(timestamp_validation.get("recommendations", []))
                    
        # 确定验证是否成功
        has_critical_issues = any(
            issue.get("severity") == "error" 
            for result in validation_results.values()
            for issue in result.get("issues", [])
        )
        
        return PipelineResult(
            success=not has_critical_issues,
            data=data,
            processing_time=0.0,
            statistics={"validation_results": validation_results},
            warnings=warnings,
            errors=errors if has_critical_issues else []
        )
        
    def _process_type_safe_concat(self, dataframes: List[pd.DataFrame],
                                **kwargs) -> PipelineResult:
        """处理类型安全的DataFrame连接"""
        return self._process_merge_dataframes(dataframes, **kwargs)
        
    def get_pipeline_statistics(self) -> Dict[str, Any]:
        """获取管道统计信息"""
        return {
            "pipeline_name": self.pipeline_name,
            "statistics": self.pipeline_statistics,
            "component_stats": {
                "data_type_manager": self.data_type_manager.get_conversion_report(),
                "enhanced_reader": self.enhanced_reader.get_statistics(),
                "smart_validator": smart_validator.get_validation_summary()
            },
            "config_summary": config_manager.get_config_summary()
        }
        
    def reset_statistics(self):
        """重置统计信息"""
        self.pipeline_statistics = {
            "total_runs": 0,
            "successful_runs": 0,
            "total_processing_time": 0.0,
            "average_processing_time": 0.0,
            "type_fixes": 0,
            "validation_failures": 0
        }
        
        self.logger.info(LogTarget.FILE, f"管道 {self.pipeline_name} 统计信息已重置")

# 全局默认管道实例
default_pipeline = UnifiedDataPipeline("default")
