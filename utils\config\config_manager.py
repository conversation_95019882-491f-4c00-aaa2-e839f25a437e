#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理系统 - 管理重构后系统的配置

该模块提供统一的配置管理，包括验证级别、错误处理策略、调试信息级别等配置。

作者: Augment Agent
创建时间: 2025-07-31
版本: 1.0.0
"""

import json
import os
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, field, asdict
from enum import Enum

from utils.logger import get_unified_logger, LogTarget

class ValidationLevel(Enum):
    """验证级别"""
    STRICT = "strict"      # 严格验证，发现问题立即停止
    NORMAL = "normal"      # 正常验证，记录问题但继续执行
    LENIENT = "lenient"    # 宽松验证，只记录严重问题

class ErrorHandlingStrategy(Enum):
    """错误处理策略"""
    FAIL_FAST = "fail_fast"        # 快速失败
    CONTINUE_ON_ERROR = "continue"  # 出错时继续
    AUTO_RECOVER = "auto_recover"   # 自动恢复

class DebugLevel(Enum):
    """调试级别"""
    NONE = "none"          # 无调试信息
    BASIC = "basic"        # 基本调试信息
    DETAILED = "detailed"  # 详细调试信息
    VERBOSE = "verbose"    # 详尽调试信息

@dataclass
class DataProcessingConfig:
    """数据处理配置"""
    validation_level: ValidationLevel = ValidationLevel.NORMAL
    error_handling_strategy: ErrorHandlingStrategy = ErrorHandlingStrategy.CONTINUE_ON_ERROR
    auto_fix_types: bool = True
    strict_type_checking: bool = True
    enable_performance_monitoring: bool = True
    max_memory_usage_mb: int = 1024
    
@dataclass
class ValidationConfig:
    """验证配置"""
    enable_time_validation: bool = True
    time_tolerance_ms: int = 1
    enable_type_validation: bool = True
    enable_range_validation: bool = True
    validation_sample_size: int = 1000
    
@dataclass
class DebugConfig:
    """调试配置"""
    debug_level: DebugLevel = DebugLevel.BASIC
    enable_data_flow_tracking: bool = True
    enable_performance_profiling: bool = True
    max_debug_history: int = 100
    debug_output_file: Optional[str] = None
    
@dataclass
class LoggingConfig:
    """日志配置"""
    log_level: str = "INFO"
    enable_file_logging: bool = True
    enable_console_logging: bool = False
    log_rotation_size_mb: int = 10
    max_log_files: int = 5
    
@dataclass
class SystemConfig:
    """系统配置"""
    data_processing: DataProcessingConfig = field(default_factory=DataProcessingConfig)
    validation: ValidationConfig = field(default_factory=ValidationConfig)
    debug: DebugConfig = field(default_factory=DebugConfig)
    logging: LoggingConfig = field(default_factory=LoggingConfig)

class ConfigManager:
    """配置管理器"""
    
    DEFAULT_CONFIG_FILE = "config/system_config.json"
    
    def __init__(self, config_file: Optional[str] = None):
        self.logger = get_unified_logger(__name__, enhanced=True)
        self.config_file = config_file or self.DEFAULT_CONFIG_FILE
        self.config = SystemConfig()
        self.config_watchers = []  # 配置变更监听器
        
        # 尝试加载配置文件
        self.load_config()
        
    def load_config(self) -> bool:
        """加载配置文件"""
        if not os.path.exists(self.config_file):
            self.logger.info(LogTarget.FILE, f"配置文件不存在，使用默认配置: {self.config_file}")
            self.save_config()  # 保存默认配置
            return True
            
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
                
            # 解析配置数据
            self._parse_config_data(config_data)
            
            self.logger.info(LogTarget.FILE, f"成功加载配置文件: {self.config_file}")
            return True

        except Exception as e:
            self.logger.error(LogTarget.FILE, f"加载配置文件失败: {e}")
            self.logger.info(LogTarget.FILE, "使用默认配置")
            return False
            
    def save_config(self) -> bool:
        """保存配置文件"""
        try:
            # 确保配置目录存在
            config_dir = os.path.dirname(self.config_file)
            if config_dir and not os.path.exists(config_dir):
                os.makedirs(config_dir)
                
            # 转换为字典格式
            config_data = self._config_to_dict()
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
                
            self.logger.info(LogTarget.FILE, f"配置文件保存成功: {self.config_file}")
            return True

        except Exception as e:
            self.logger.error(LogTarget.FILE, f"保存配置文件失败: {e}")
            return False
            
    def _parse_config_data(self, config_data: Dict[str, Any]):
        """解析配置数据"""
        # 解析数据处理配置
        if "data_processing" in config_data:
            dp_config = config_data["data_processing"]
            self.config.data_processing = DataProcessingConfig(
                validation_level=ValidationLevel(dp_config.get("validation_level", "normal")),
                error_handling_strategy=ErrorHandlingStrategy(dp_config.get("error_handling_strategy", "continue")),
                auto_fix_types=dp_config.get("auto_fix_types", True),
                strict_type_checking=dp_config.get("strict_type_checking", True),
                enable_performance_monitoring=dp_config.get("enable_performance_monitoring", True),
                max_memory_usage_mb=dp_config.get("max_memory_usage_mb", 1024)
            )
            
        # 解析验证配置
        if "validation" in config_data:
            val_config = config_data["validation"]
            self.config.validation = ValidationConfig(
                enable_time_validation=val_config.get("enable_time_validation", True),
                time_tolerance_ms=val_config.get("time_tolerance_ms", 1),
                enable_type_validation=val_config.get("enable_type_validation", True),
                enable_range_validation=val_config.get("enable_range_validation", True),
                validation_sample_size=val_config.get("validation_sample_size", 1000)
            )
            
        # 解析调试配置
        if "debug" in config_data:
            debug_config = config_data["debug"]
            self.config.debug = DebugConfig(
                debug_level=DebugLevel(debug_config.get("debug_level", "basic")),
                enable_data_flow_tracking=debug_config.get("enable_data_flow_tracking", True),
                enable_performance_profiling=debug_config.get("enable_performance_profiling", True),
                max_debug_history=debug_config.get("max_debug_history", 100),
                debug_output_file=debug_config.get("debug_output_file")
            )
            
        # 解析日志配置
        if "logging" in config_data:
            log_config = config_data["logging"]
            self.config.logging = LoggingConfig(
                log_level=log_config.get("log_level", "INFO"),
                enable_file_logging=log_config.get("enable_file_logging", True),
                enable_console_logging=log_config.get("enable_console_logging", False),
                log_rotation_size_mb=log_config.get("log_rotation_size_mb", 10),
                max_log_files=log_config.get("max_log_files", 5)
            )
            
    def _config_to_dict(self) -> Dict[str, Any]:
        """将配置转换为字典格式"""
        return {
            "data_processing": {
                "validation_level": self.config.data_processing.validation_level.value,
                "error_handling_strategy": self.config.data_processing.error_handling_strategy.value,
                "auto_fix_types": self.config.data_processing.auto_fix_types,
                "strict_type_checking": self.config.data_processing.strict_type_checking,
                "enable_performance_monitoring": self.config.data_processing.enable_performance_monitoring,
                "max_memory_usage_mb": self.config.data_processing.max_memory_usage_mb
            },
            "validation": {
                "enable_time_validation": self.config.validation.enable_time_validation,
                "time_tolerance_ms": self.config.validation.time_tolerance_ms,
                "enable_type_validation": self.config.validation.enable_type_validation,
                "enable_range_validation": self.config.validation.enable_range_validation,
                "validation_sample_size": self.config.validation.validation_sample_size
            },
            "debug": {
                "debug_level": self.config.debug.debug_level.value,
                "enable_data_flow_tracking": self.config.debug.enable_data_flow_tracking,
                "enable_performance_profiling": self.config.debug.enable_performance_profiling,
                "max_debug_history": self.config.debug.max_debug_history,
                "debug_output_file": self.config.debug.debug_output_file
            },
            "logging": {
                "log_level": self.config.logging.log_level,
                "enable_file_logging": self.config.logging.enable_file_logging,
                "enable_console_logging": self.config.logging.enable_console_logging,
                "log_rotation_size_mb": self.config.logging.log_rotation_size_mb,
                "max_log_files": self.config.logging.max_log_files
            }
        }
        
    def get_config(self) -> SystemConfig:
        """获取当前配置"""
        return self.config
        
    def update_config(self, **kwargs) -> bool:
        """更新配置"""
        try:
            # 更新数据处理配置
            if "data_processing" in kwargs:
                dp_updates = kwargs["data_processing"]
                for key, value in dp_updates.items():
                    if hasattr(self.config.data_processing, key):
                        setattr(self.config.data_processing, key, value)
                        
            # 更新验证配置
            if "validation" in kwargs:
                val_updates = kwargs["validation"]
                for key, value in val_updates.items():
                    if hasattr(self.config.validation, key):
                        setattr(self.config.validation, key, value)
                        
            # 更新调试配置
            if "debug" in kwargs:
                debug_updates = kwargs["debug"]
                for key, value in debug_updates.items():
                    if hasattr(self.config.debug, key):
                        setattr(self.config.debug, key, value)
                        
            # 更新日志配置
            if "logging" in kwargs:
                log_updates = kwargs["logging"]
                for key, value in log_updates.items():
                    if hasattr(self.config.logging, key):
                        setattr(self.config.logging, key, value)
                        
            # 保存配置
            self.save_config()
            
            # 通知配置变更监听器
            self._notify_config_watchers()
            
            self.logger.info(LogTarget.FILE, "配置更新成功")
            return True

        except Exception as e:
            self.logger.error(LogTarget.FILE, f"配置更新失败: {e}")
            return False
            
    def add_config_watcher(self, callback):
        """添加配置变更监听器"""
        self.config_watchers.append(callback)
        
    def _notify_config_watchers(self):
        """通知配置变更监听器"""
        for callback in self.config_watchers:
            try:
                callback(self.config)
            except Exception as e:
                self.logger.warning(LogTarget.FILE, f"配置监听器执行失败: {e}")

    def reset_to_defaults(self):
        """重置为默认配置"""
        self.config = SystemConfig()
        self.save_config()
        self._notify_config_watchers()
        self.logger.info(LogTarget.FILE, "配置已重置为默认值")
        
    def get_config_summary(self) -> Dict[str, Any]:
        """获取配置摘要"""
        return {
            "config_file": self.config_file,
            "validation_level": self.config.data_processing.validation_level.value,
            "error_handling": self.config.data_processing.error_handling_strategy.value,
            "debug_level": self.config.debug.debug_level.value,
            "auto_fix_enabled": self.config.data_processing.auto_fix_types,
            "performance_monitoring": self.config.data_processing.enable_performance_monitoring
        }

# 全局配置管理器实例
config_manager = ConfigManager()
