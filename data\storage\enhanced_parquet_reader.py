#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强的Parquet读取器 - 替换现有的parquet_reader，集成类型安全

该模块提供类型安全的数据读取和合并功能，严格遵循项目规范，使用IndexManager.safe_concat。

作者: Augment Agent
创建时间: 2025-07-31
版本: 1.0.0
"""

import os
import time
import pandas as pd
from typing import List, Dict, Any, Optional, Tuple
import glob

from utils.logger import get_unified_logger, LogTarget
from utils.data_processor.data_type_manager import DataTypeManager
from utils.error_handling.unified_error_handler import (
    error_handler, ErrorCode, ErrorCategory, ErrorSeverity
)
from utils.debug.debug_info_system import debug_system
from utils.config.config_manager import config_manager
from data.storage.parquet_reader import filter_data_by_time_range

class EnhancedParquetReader:
    """增强的Parquet读取器"""
    
    def __init__(self, enable_type_checking: bool = True,
                 enable_performance_monitoring: bool = True):
        """
        初始化增强的Parquet读取器

        Args:
            enable_type_checking: 是否启用类型检查
            enable_performance_monitoring: 是否启用性能监控
        """
        self.logger = get_unified_logger(__name__, enhanced=True)
        self.enable_type_checking = enable_type_checking
        self.enable_performance_monitoring = enable_performance_monitoring
        
        # 获取配置
        config = config_manager.get_config()
        self.data_type_manager = DataTypeManager(
            strict_mode=(config.data_processing.validation_level.value == "strict"),
            auto_fix=config.data_processing.auto_fix_types
        )
        
        # 统计信息
        self.read_statistics = {
            "total_reads": 0,
            "successful_reads": 0,
            "type_fixes": 0,
            "performance_issues": 0
        }
        
    def read_partitioned_data(self, data_root: str, symbol: str, period: str,
                            start_time: str = None, end_time: str = None,
                            columns: List[str] = None, data_type: str = "raw",
                            adj_type: str = None) -> Optional[pd.DataFrame]:
        """
        读取分区数据，确保类型安全
        
        Args:
            data_root: 数据根目录
            symbol: 股票代码
            period: 数据周期
            start_time: 开始时间
            end_time: 结束时间
            columns: 要读取的列
            data_type: 数据类型
            adj_type: 复权类型
            
        Returns:
            pd.DataFrame: 读取的数据
        """
        self.read_statistics["total_reads"] += 1
        
        # 创建错误上下文
        context = error_handler.create_context(
            function_name="read_partitioned_data",
            module_name="enhanced_parquet_reader",
            operation="读取分区数据",
            symbol=symbol,
            period=period,
            start_time=start_time,
            end_time=end_time
        )
        
        # 启动数据流跟踪
        if debug_system.is_debug_enabled("data_flow"):
            debug_system.data_flow_tracker.start_flow(
                f"read_partitioned_data_{symbol}_{period}"
            )
            
        # 启动性能监控
        with debug_system.performance_monitor.monitor_operation(
            f"read_partitioned_data_{symbol}_{period}"
        ):
            try:
                # 获取分区文件列表
                partition_files = self._get_partition_files(
                    data_root, symbol, period, start_time, end_time, adj_type
                )
                
                if not partition_files:
                    self.logger.warning(LogTarget.FILE,
                                 f"未找到匹配的分区文件: {symbol} {period}")
                    return None

                self.logger.debug(LogTarget.FILE,
                           f"找到 {len(partition_files)} 个分区文件")
                
                # 添加数据流步骤
                if debug_system.is_debug_enabled("data_flow"):
                    debug_system.data_flow_tracker.add_step(
                        "partition_files_found",
                        pd.DataFrame(),  # 空DataFrame作为占位符
                        metadata={"file_count": len(partition_files)}
                    )
                
                # 读取分区文件
                dfs = self._read_partition_files(partition_files, columns, context)
                
                if not dfs:
                    self.logger.warning(LogTarget.FILE, "所有分区文件读取失败")
                    return None

                # 类型安全合并
                merged_df = self._safe_merge_dataframes(dfs, context)

                if merged_df is None:
                    self.logger.error(LogTarget.FILE, "数据合并失败")
                    return None
                    
                # 时间范围过滤
                if start_time or end_time:
                    filtered_df = filter_data_by_time_range(merged_df, start_time, end_time)
                    
                    if debug_system.is_debug_enabled("data_flow"):
                        debug_system.data_flow_tracker.add_step(
                            "time_range_filter",
                            filtered_df,
                            metadata={
                                "original_rows": len(merged_df),
                                "filtered_rows": len(filtered_df) if filtered_df is not None else 0
                            }
                        )
                        
                    merged_df = filtered_df
                    
                # 最终验证
                if self.enable_type_checking and merged_df is not None:
                    self._validate_final_result(merged_df, context)
                    
                # 更新统计信息
                if merged_df is not None:
                    self.read_statistics["successful_reads"] += 1
                    
                # 结束数据流跟踪
                if debug_system.is_debug_enabled("data_flow"):
                    flow_summary = debug_system.data_flow_tracker.end_flow()
                    self.logger.debug(LogTarget.FILE,
                               f"数据流跟踪完成: {flow_summary.get('flow_name', 'unknown')}")
                    
                return merged_df
                
            except Exception as e:
                # 处理错误
                error_info = error_handler.handle_error(
                    ErrorCode.FIO_READ_FAILED,
                    f"分区数据读取失败: {str(e)}",
                    context,
                    details=str(e),
                    suggestions=[
                        "检查数据文件是否存在",
                        "验证文件权限",
                        "检查数据格式是否正确"
                    ]
                )
                
                self.logger.error(LogTarget.FILE, f"读取分区数据时发生异常: {e}")
                return None
                
    def _get_partition_files(self, data_root: str, symbol: str, period: str,
                           start_time: str = None, end_time: str = None,
                           adj_type: str = None) -> List[str]:
        """获取分区文件列表"""
        # 构建文件路径模式
        if adj_type:
            pattern = os.path.join(data_root, adj_type, symbol.split('.')[0], 
                                 symbol.split('.')[1], period, "**", "*.parquet")
        else:
            pattern = os.path.join(data_root, "raw", symbol.split('.')[0], 
                                 symbol.split('.')[1], period, "**", "*.parquet")
                                 
        # 获取所有匹配的文件
        files = glob.glob(pattern, recursive=True)
        
        # 如果指定了时间范围，进行文件过滤
        if start_time or end_time:
            files = self._filter_files_by_time(files, start_time, end_time)
            
        return sorted(files)
        
    def _filter_files_by_time(self, files: List[str], start_time: str, 
                             end_time: str) -> List[str]:
        """根据时间范围过滤文件"""
        # 这里可以根据文件名中的日期信息进行过滤
        # 简化实现，返回所有文件
        return files
        
    def _read_partition_files(self, files: List[str], columns: List[str],
                            context) -> List[pd.DataFrame]:
        """读取分区文件"""
        dfs = []
        failed_files = []
        
        for i, file_path in enumerate(files):
            try:
                # 读取单个文件
                df = pd.read_parquet(file_path, columns=columns)
                
                if df is not None and not df.empty:
                    # 类型检查
                    if self.enable_type_checking:
                        analysis = self.data_type_manager.analyze_dataframe_types(
                            df, f"file_{i}"
                        )
                        
                        if analysis.get("issues"):
                            self.logger.debug(LogTarget.FILE,
                                       f"文件 {os.path.basename(file_path)} 存在类型问题: "
                                       f"{len(analysis['issues'])} 个")
                            
                            # 尝试修复类型问题
                            df = self.data_type_manager._fix_column_type(df, 'time')
                            self.read_statistics["type_fixes"] += 1
                            
                    dfs.append(df)
                    
                    # 添加调试信息
                    if debug_system.is_debug_enabled("data_flow"):
                        debug_system.data_flow_tracker.add_step(
                            f"read_file_{i}",
                            df,
                            metadata={"file_path": os.path.basename(file_path)}
                        )
                        
            except Exception as e:
                failed_files.append(file_path)
                self.logger.warning(LogTarget.FILE,
                             f"读取文件失败: {os.path.basename(file_path)}, 错误: {e}")

        if failed_files:
            self.logger.warning(LogTarget.FILE,
                         f"共有 {len(failed_files)} 个文件读取失败")
            
        return dfs
        
    def _safe_merge_dataframes(self, dfs: List[pd.DataFrame], context) -> Optional[pd.DataFrame]:
        """安全合并DataFrame"""
        if not dfs:
            return None
            
        if len(dfs) == 1:
            return dfs[0]
            
        try:
            # 使用DataTypeManager进行类型安全合并
            merged_df = self.data_type_manager.safe_concat_with_type_check(
                dfs, f"{context.operation}_merge"
            )
            
            if debug_system.is_debug_enabled("data_flow"):
                debug_system.data_flow_tracker.add_step(
                    "safe_merge",
                    merged_df,
                    metadata={
                        "input_dataframes": len(dfs),
                        "total_input_rows": sum(len(df) for df in dfs)
                    }
                )
                
            self.logger.debug(LogTarget.FILE,
                       f"成功合并 {len(dfs)} 个DataFrame，结果: {merged_df.shape if merged_df is not None else 'None'}")
            
            return merged_df
            
        except Exception as e:
            # 处理合并错误
            error_info = error_handler.handle_error(
                ErrorCode.DT_INCONSISTENT_TYPES,
                f"DataFrame合并失败: {str(e)}",
                context,
                details=str(e),
                suggestions=[
                    "检查数据类型一致性",
                    "使用DataTypeManager修复类型问题",
                    "检查索引格式是否正确"
                ]
            )
            
            return None
            
    def _validate_final_result(self, df: pd.DataFrame, context):
        """验证最终结果"""
        if df is None or df.empty:
            return
            
        # 进行最终的类型验证
        analysis = self.data_type_manager.analyze_dataframe_types(df, "final_result")
        
        if analysis.get("issues"):
            self.logger.warning(LogTarget.FILE,
                         f"最终结果存在类型问题: {len(analysis['issues'])} 个")
            
            for issue in analysis["issues"]:
                if issue.get("severity") == "error":
                    error_handler.handle_error(
                        ErrorCode.DT_TYPE_MISMATCH,
                        f"最终结果类型问题: {issue.get('description', 'unknown')}",
                        context,
                        suggestions=analysis.get("recommendations", [])
                    )
                    
    def get_statistics(self) -> Dict[str, Any]:
        """获取读取统计信息"""
        return {
            **self.read_statistics,
            "success_rate": (
                self.read_statistics["successful_reads"] / 
                max(1, self.read_statistics["total_reads"])
            ),
            "type_manager_report": self.data_type_manager.get_conversion_report()
        }
