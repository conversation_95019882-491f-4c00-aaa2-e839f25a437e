{"data_processing": {"validation_level": "normal", "error_handling_strategy": "continue", "auto_fix_types": true, "strict_type_checking": true, "enable_performance_monitoring": true, "max_memory_usage_mb": 1024}, "validation": {"enable_time_validation": true, "time_tolerance_ms": 1, "enable_type_validation": true, "enable_range_validation": true, "validation_sample_size": 1000}, "debug": {"debug_level": "basic", "enable_data_flow_tracking": true, "enable_performance_profiling": true, "max_debug_history": 100, "debug_output_file": null}, "logging": {"log_level": "INFO", "enable_file_logging": true, "enable_console_logging": false, "log_rotation_size_mb": 10, "max_log_files": 5}}